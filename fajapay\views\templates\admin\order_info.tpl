<div class="card">
    <div class="card-header">
        <h3 class="card-header-title">
            <i class="material-icons">payment</i>
            {l s='UPI Payment Information' d='Modules.Fajapay.Admin'}
        </h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-lg-6">
                <h4 class="mb-3">{l s='Order Details' d='Modules.Fajapay.Admin'}</h4>
                <table class="table table-borderless">
                    <tr>
                        <td><strong>{l s='Order Reference:' d='Modules.Fajapay.Admin'}</strong></td>
                        <td><span class="badge badge-secondary">{$order_reference}</span></td>
                    </tr>
                    <tr>
                        <td><strong>{l s='Invoice Number:' d='Modules.Fajapay.Admin'}</strong></td>
                        <td>
                            {if $invoice_number}
                                <span class="badge badge-info">{$invoice_number}</span>
                            {else}
                                <span class="text-muted">{l s='Not generated yet' d='Modules.Fajapay.Admin'}</span>
                            {/if}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>{l s='Order Total:' d='Modules.Fajapay.Admin'}</strong></td>
                        <td><span class="text-primary font-weight-bold">{$currency_sign}{$order_total}</span></td>
                    </tr>
                </table>
            </div>

            <div class="col-lg-6">
                <h4 class="mb-3">{l s='Payment Details' d='Modules.Fajapay.Admin'}</h4>
                <table class="table table-borderless">
                    <tr>
                        <td><strong>{l s='UTR Number:' d='Modules.Fajapay.Admin'}</strong></td>
                        <td>
                            {if $utr_number}
                                <span class="badge badge-success">{$utr_number}</span>
                            {else}
                                <span class="text-muted">{l s='Not provided yet' d='Modules.Fajapay.Admin'}</span>
                            {/if}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>{l s='Payment Status:' d='Modules.Fajapay.Admin'}</strong></td>
                        <td>
                            {if $payment_status == 'verified'}
                                <span class="badge badge-success">
                                    <i class="material-icons">check_circle</i>
                                    {l s='Verified' d='Modules.Fajapay.Admin'}
                                </span>
                            {elseif $payment_status == 'failed'}
                                <span class="badge badge-danger">
                                    <i class="material-icons">error</i>
                                    {l s='Failed' d='Modules.Fajapay.Admin'}
                                </span>
                            {else}
                                <span class="badge badge-warning">
                                    <i class="material-icons">schedule</i>
                                    {l s='Pending Verification' d='Modules.Fajapay.Admin'}
                                </span>
                            {/if}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>{l s='Transaction Amount:' d='Modules.Fajapay.Admin'}</strong></td>
                        <td>
                            {if $transaction_amount}
                                <span class="text-success font-weight-bold">{$currency_sign}{$transaction_amount}</span>
                            {else}
                                <span class="text-muted">-</span>
                            {/if}
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <hr class="my-4">

        <div class="row">
            <div class="col-lg-12">
                {if $utr_number}
                    <div class="alert alert-info">
                        <i class="material-icons">info</i>
                        <strong>{l s='Verification Required:' d='Modules.Fajapay.Admin'}</strong>
                        {l s='Please verify this UTR number in your UPI app or bank account before processing the order.' d='Modules.Fajapay.Admin'}
                    </div>

                    {if $payment_status == 'pending'}
                        <div class="alert alert-warning">
                            <i class="material-icons">warning</i>
                            <strong>{l s='Action Required:' d='Modules.Fajapay.Admin'}</strong>
                            {l s='This payment is still pending verification. Please check your UPI transaction history.' d='Modules.Fajapay.Admin'}
                        </div>
                    {/if}
                {else}
                    <div class="alert alert-secondary">
                        <i class="material-icons">schedule</i>
                        <strong>{l s='Waiting for Payment:' d='Modules.Fajapay.Admin'}</strong>
                        {l s='Customer has not provided UTR number yet. Payment confirmation is pending.' d='Modules.Fajapay.Admin'}
                    </div>
                {/if}
            </div>
        </div>
    </div>
</div>
