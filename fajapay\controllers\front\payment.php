<?php
/**
 * 2025 FajaPay
 *
 * NOTICE OF LICENSE
 *
 * Developed by Fowzana Soft Solutions (ZuhayFaja Group)
 *
 * <AUTHOR>
 * @copyright 2025 FajaPay
 * @license   Proprietary License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class FajaPayPaymentModuleFrontController extends ModuleFrontController
{
    public $ssl = true;

    /**
     * Initialize controller
     */
    public function init()
    {
        parent::init();

        // Initialize shop context
        if (Shop::isFeatureActive()) {
            Shop::setContext(Shop::CONTEXT_SHOP, (int)$this->context->shop->id);
        }

        // Validate cart existence
        if (!$this->context->cart || !$this->context->cart->id) {
            Tools::redirect('index.php?controller=order');
        }

        // Validate cart object
        $cart = new Cart($this->context->cart->id);
        if (!Validate::isLoadedObject($cart)) {
            Tools::redirect('index.php?controller=order');
        }
    }

    public function initContent()
    {
        parent::initContent();

        $cart = $this->context->cart;
        if (!$this->module->checkCurrency($cart)) {
            Tools::redirect('index.php?controller=order');
        }

        // Verify module configuration
        $upiId = Configuration::get('FAJAPAY_UPI_ID');
        $shopName = Configuration::get('FAJAPAY_SHOP_NAME');
        if (empty($upiId) || empty($shopName)) {
            $this->errors[] = $this->trans('Payment method not properly configured.', [], 'Modules.Fajapay.Shop');
            return;
        }

        $this->context->smarty->assign([
            'nbProducts' => $cart->nbProducts(),
            'total' => $cart->getOrderTotal(true, Cart::BOTH),
            'upiId' => $upiId,
            'shopName' => $shopName,
            'enableQrRefresh' => (bool)Configuration::get('FAJAPAY_ENABLE_QR_REFRESH'),
            'qrRefreshInterval' => (int)Configuration::get('FAJAPAY_QR_REFRESH_INTERVAL'),
            'this_path' => $this->module->getPathUri(),
            'this_path_ssl' => Tools::getShopDomainSsl(true, true).__PS_BASE_URI__.'modules/'.$this->module->name.'/',
        ]);

        $this->setTemplate('module:fajapay/views/templates/front/payment.tpl');
    }

    public function postProcess()
    {
        if (!Tools::isSubmit('utr_number')) {
            return;
        }

        // Validate cart
        $cart = $this->context->cart;
        if ($cart->id_customer == 0 || $cart->id_address_delivery == 0 ||
            $cart->id_address_invoice == 0 || !$this->module->active) {
            Tools::redirect('index.php?controller=order&step=1');
        }

        // Check if this payment option is still available
        $authorized = false;
        foreach (Module::getPaymentModules() as $module) {
            if ($module['name'] == 'fajapay') {
                $authorized = true;
                break;
            }
        }

        if (!$authorized) {
            die($this->trans('This payment method is not available.', [], 'Modules.Fajapay.Shop'));
        }

        // Validate customer
        $customer = new Customer($cart->id_customer);
        if (!Validate::isLoadedObject($customer)) {
            Tools::redirect('index.php?controller=order&step=1');
        }

        // Get payment details
        $currency = $this->context->currency;
        $total = (float)$cart->getOrderTotal(true, Cart::BOTH);
        $utr = Tools::getValue('utr_number');

        if (empty($utr)) {
            $this->errors[] = $this->trans('UTR number is required.', [], 'Modules.Fajapay.Shop');
            return;
        }

        try {
            // Create the order
            $this->module->validateOrder(
                (int)$cart->id,
                Configuration::get('FAJAPAY_OS_WAITING'),
                $total,
                $this->module->displayName,
                'UTR: ' . $utr,
                [],
                (int)$currency->id,
                false,
                $customer->secure_key
            );

            // Get the created order
            $order = new Order($this->module->currentOrder);
            if (Validate::isLoadedObject($order)) {
                // Store UTR in database
                $this->storeUTR($order->id, $utr, $total);

                // Add UTR as order message (for backward compatibility)
                $message = new Message();
                $message->message = 'Customer provided UTR: ' . $utr;
                $message->id_order = (int)$order->id;
                $message->id_customer = (int)$customer->id;
                $message->private = 0;
                $message->add();
            }

            // Redirect to order details instead of order confirmation
            Tools::redirect('index.php?controller=order-detail&id_order='.$this->module->currentOrder);

        } catch (Exception $e) {
            $this->errors[] = $this->trans('An error occurred while processing your payment.', [], 'Modules.Fajapay.Shop');
            PrestaShopLogger::addLog('FajaPay payment error: '.$e->getMessage(), 3, null, 'Cart', $cart->id);
        }
    }

    /**
     * Store UTR in database
     */
    private function storeUTR($orderId, $utrNumber, $amount)
    {
        $sql = 'INSERT INTO `'._DB_PREFIX_.'fajapay_transactions`
                (id_order, utr_number, transaction_amount, payment_status, date_add, date_upd)
                VALUES ('.(int)$orderId.', "'.pSQL($utrNumber).'", '.(float)$amount.', "pending", NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                utr_number = "'.pSQL($utrNumber).'",
                transaction_amount = '.(float)$amount.',
                date_upd = NOW()';

        if (!Db::getInstance()->execute($sql)) {
            throw new Exception('Failed to store UTR in database');
        }
    }
}
