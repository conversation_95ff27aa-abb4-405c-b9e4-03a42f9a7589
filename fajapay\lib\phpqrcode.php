<?php

/*
 * PHP QR Code encoder
 * Lightweight QR Code Generator
 */

class PhpQrCode {
    private $data;
    private $size;
    private $margin;
    private $errorLevel;
    
    public function __construct($data, $size = 300, $margin = 10) {
        $this->data = $data;
        $this->size = $size;
        $this->margin = $margin;
        $this->errorLevel = 'H'; // High error correction
    }
    
    public function getDataUri() {
        // Create QR code matrix
        $matrix = $this->createMatrix();
        if (!$matrix) {
            return false;
        }
        
        // Create image
        $image = $this->createImage($matrix);
        if (!$image) {
            return false;
        }
        
        // Capture image data
        ob_start();
        imagepng($image);
        $imageData = ob_get_clean();
        
        // Clean up
        imagedestroy($image);
        
        // Return base64 encoded image
        return 'data:image/png;base64,' . base64_encode($imageData);
    }
    
    private function createMatrix() {
        $matrixSize = $this->size - (2 * $this->margin);
        $matrix = array_fill(0, $matrixSize, array_fill(0, $matrixSize, 0));
        
        // Generate basic pattern
        $this->generateBasicPattern($matrix);
        
        // Add data pattern
        $this->addDataPattern($matrix);
        
        return $matrix;
    }
    
    private function createImage($matrix) {
        $image = imagecreatetruecolor($this->size, $this->size);
        if (!$image) {
            return false;
        }
        
        // Set colors
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);
        
        // Fill background
        imagefilledrectangle($image, 0, 0, $this->size - 1, $this->size - 1, $white);
        
        // Draw matrix
        $blockSize = ($this->size - (2 * $this->margin)) / count($matrix);
        foreach ($matrix as $y => $row) {
            foreach ($row as $x => $value) {
                if ($value) {
                    $startX = $this->margin + ($x * $blockSize);
                    $startY = $this->margin + ($y * $blockSize);
                    imagefilledrectangle(
                        $image,
                        $startX,
                        $startY,
                        $startX + $blockSize - 1,
                        $startY + $blockSize - 1,
                        $black
                    );
                }
            }
        }
        
        return $image;
    }
    
    private function generateBasicPattern(&$matrix) {
        $size = count($matrix);
        
        // Add finder patterns
        $this->addFinderPattern($matrix, 0, 0);
        $this->addFinderPattern($matrix, $size - 7, 0);
        $this->addFinderPattern($matrix, 0, $size - 7);
        
        // Add timing patterns
        for ($i = 8; $i < $size - 8; $i++) {
            $matrix[6][$i] = ($i % 2 == 0) ? 1 : 0;
            $matrix[$i][6] = ($i % 2 == 0) ? 1 : 0;
        }
    }
    
    private function addFinderPattern(&$matrix, $startX, $startY) {
        // Outer square
        for ($y = 0; $y < 7; $y++) {
            for ($x = 0; $x < 7; $x++) {
                if ($x == 0 || $x == 6 || $y == 0 || $y == 6) {
                    $matrix[$startY + $y][$startX + $x] = 1;
                }
            }
        }
        
        // Inner square
        for ($y = 2; $y < 5; $y++) {
            for ($x = 2; $x < 5; $x++) {
                $matrix[$startY + $y][$startX + $x] = 1;
            }
        }
    }
    
    private function addDataPattern(&$matrix) {
        // Convert data to binary
        $binary = $this->dataToBinary();
        
        // Add data bits to matrix
        $size = count($matrix);
        $index = 0;
        $up = true;
        
        for ($x = $size - 1; $x >= 0; $x -= 2) {
            if ($x <= 6) {
                $x--;
            }
            
            for ($y = ($up ? $size - 1 : 0); $up ? $y >= 0 : $y < $size; $y += ($up ? -1 : 1)) {
                for ($i = 0; $i < 2; $i++) {
                    $xPos = $x - $i;
                    if ($xPos >= 0 && isset($binary[$index])) {
                        $matrix[$y][$xPos] = $binary[$index++];
                    }
                }
            }
            
            $up = !$up;
        }
    }
    
    private function dataToBinary() {
        // Simple implementation - convert each character to binary
        $binary = [];
        $data = $this->data;
        
        for ($i = 0; $i < strlen($data); $i++) {
            $char = ord($data[$i]);
            for ($j = 7; $j >= 0; $j--) {
                $binary[] = ($char >> $j) & 1;
            }
        }
        
        // Add terminator
        for ($i = 0; $i < 4; $i++) {
            $binary[] = 0;
        }
        
        return $binary;
    }
}
