<?php
/**
 * 2025 FajaPay
 *
 * NOTICE OF LICENSE
 *
 * Developed by Fowzana Soft Solutions (ZuhayFaja Group)
 *
 * <AUTHOR>
 * @copyright 2025 FajaPay
 * @license   Proprietary License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_ . 'fajapay/lib/phpqrcode.php';

class FajaPayAjaxModuleFrontController extends ModuleFrontController
{
    private function sendJsonResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        die(json_encode($data));
    }

    public function init()
    {
        parent::init();
        $this->ajax = true;
        $this->content_only = true;
        
        // Initialize shop context
        if (Shop::isFeatureActive()) {
            Shop::setContext(Shop::CONTEXT_SHOP, (int)$this->context->shop->id);
        }
    }

    public function initContent()
    {
        try {
            // Verify AJAX request
            if (!$this->ajax || empty($_SERVER['HTTP_X_REQUESTED_WITH']) || 
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
                throw new Exception('Invalid request type', 400);
            }

            // Security token validation
            $token = Tools::getValue('token');
            if (empty($token) || $token !== Tools::getToken(false)) {
                throw new Exception('Invalid security token', 403);
            }

            // Process action
            $action = Tools::getValue('action');
            if (empty($action)) {
                throw new Exception('No action specified', 400);
            }

            switch ($action) {
                case 'refreshQr':
                    $response = $this->refreshQrCode();
                    break;
                default:
                    throw new Exception('Invalid action: ' . $action, 400);
            }

            $this->sendJsonResponse($response);

        } catch (Exception $e) {
            $this->sendJsonResponse([
                'status' => 'error',
                'message' => $e->getMessage()
            ], $e->getCode() ?: 500);
        }
    }

    protected function refreshQrCode()
    {
        if (!$this->module->active) {
            throw new Exception('Module is not active', 400);
        }

        if (!$this->context->cart || !$this->context->cart->id) {
            throw new Exception('No active cart found', 400);
        }

        $cart = new Cart($this->context->cart->id);
        if (!Validate::isLoadedObject($cart)) {
            throw new Exception('Invalid cart', 400);
        }

        $upiId = Configuration::get('FAJAPAY_UPI_ID');
        $shopName = Configuration::get('FAJAPAY_SHOP_NAME');
        
        if (empty($upiId) || empty($shopName)) {
            throw new Exception('Module not properly configured', 500);
        }

        $total = $cart->getOrderTotal(true, Cart::BOTH);
        $reference = $cart->id . '-' . time();

        // Create UPI payment string
        $upiString = sprintf(
            'upi://pay?pa=%s&pn=%s&am=%.2f&tn=%s&tr=%s',
            urlencode($upiId),
            urlencode($shopName),
            $total,
            urlencode("Order Payment"),
            urlencode($reference)
        );

        try {
            // Check GD library
            if (!extension_loaded('gd')) {
                throw new Exception('GD library is required for QR code generation');
            }

            // Generate QR code
            $qrCode = new PhpQrCode($upiString, 300, 10);
            $qrImage = $qrCode->getDataUri();
            
            if (!$qrImage) {
                throw new Exception('Failed to generate QR code image');
            }

            // Return the QR code data
            return [
                'status' => 'success',
                'data' => [
                    'qrCode' => $qrImage,
                    'reference' => $reference,
                    'upiString' => $upiString
                ]
            ];
        } catch (Exception $e) {
            throw new Exception('QR code generation failed: ' . $e->getMessage(), 500);
        }
    }
}
