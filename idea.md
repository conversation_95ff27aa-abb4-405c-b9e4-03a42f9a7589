Project Title: FajaPay (PrestaShop UPI Payment Module with Manual Verification & Enhanced Features)
Name: FajaPay 
Author: Dr.wolfsha
copyright :  2025 FajaPay
Developed by: Fowzana Soft Solutions (ZuhayFaja Group)
Version: 0.0.1

Overall Goal:
To provide PrestaShop merchants in India with a flexible and user-friendly payment module to accept payments via the Unified Payments Interface (UPI). The module focuses on a manual verification workflow, augmented by features to improve merchant administration, customer experience, and branding.
Core Payment Flow:
Customer Checkout: Customer selects "Pay with UPI" (or similar) at checkout.
Payment Page/Popup Display:
The system creates an order in PrestaShop with a custom "Awaiting UPI Confirmation" status.
A dedicated payment page (or modal popup) is displayed to the customer containing:
Shop Branding: A merchant-uploaded logo specifically for the payment page (fallback to PrestaShop's main logo if not set).
Order Details: Clearly visible Order Reference and Total Amount payable.
UPI QR Code:
Dynamically generated, containing merchant's UPI ID, shop name, amount, currency, and the Order Reference as a transaction note/ID.
Optionally embeds the PrestaShop main store logo in the center of the QR.
Dynamic Refresh: If enabled by the merchant, the QR code image periodically refreshes via an AJAX call, potentially with an updated transaction reference for enhanced security or session management. A countdown timer informs the user of the next refresh.
UPI Deep Links: Clickable links that attempt to open popular UPI apps (Google Pay, PhonePe, Paytm, generic UPI) pre-filled with payment details for mobile users.
Customer Payment: Customer scans the QR code or uses a deep link to make the payment via their UPI app.
Transaction ID Submission: After successful payment, the customer obtains a Transaction ID (UTR) from their UPI app and enters it into a form on the payment page.
Order Update (Initial): Upon UTR submission, the system adds the UTR as a private message to the PrestaShop order. The order remains in "Awaiting UPI Confirmation." Customer is redirected to the standard order confirmation page.
Merchant Verification & Fulfillment:
Merchant sees the order in the "Awaiting UPI Confirmation" state in the Back Office.
The submitted UTR is visible in the order details.
Merchant manually verifies the payment receipt in their bank account/UPI app using the UTR and amount.
If confirmed, merchant updates the order status to "Payment Accepted," "Processing," etc., and proceeds with fulfillment.
Key Module Components & Features:
I. Merchant Configuration (Back Office):
Basic Settings:
Merchant's UPI ID.
Merchant's Shop Name (for display in UPI apps).
Branding & Display:
Payment Page Logo Upload: Ability to upload a custom logo specifically for the UPI payment page. Includes a preview and delete option for the current logo.
QR Code Settings:
Enable/Disable Dynamic QR Code Refresh.
QR Code Refresh Interval: Configurable time (in seconds) for how often the QR code should refresh.
Custom Order State: Automatically creates and uses a dedicated order state (e.g., "Awaiting UPI Confirmation") upon installation.
II. Frontend - Customer Facing (Payment Page/Popup):
Clear Instructions: Guides the customer through the payment process.
Visual Consistency: Displays the uploaded payment page logo.
QR Code Display: Renders the generated QR code image.
If dynamic refresh is enabled, JavaScript handles periodic AJAX calls to a module controller action to get a new QR code image data URI and updates the displayed image.
A visual countdown indicates when the next refresh will occur.
UPI App Deep Links: Facilitates easy payment for mobile users.
UTR Submission Form: Securely collects the customer's UPI Transaction ID.
Responsive Design: (Implicitly expected) The payment page should be usable on various devices.
III. Backend Logic & Controllers:
Main Module File (myupi.php):
Handles installation/uninstallation (including custom order state creation, directory creation for logos, configuration variable setup/cleanup).
Manages module configuration form rendering and processing (including file uploads for the logo).
Registers necessary hooks (paymentOptions, displayAdminOrderLeft).
Contains the QR code generation logic using the endroid/qr-code library (including optional embedding of PS_LOGO).
Payment Controller (controllers/front/payment.php):
Handles the initial display of the payment page/popup.
Creates the order with the custom pending status.
Prepares all data for the payment template (UPI string, QR URI, logo URLs, refresh settings, AJAX URLs).
Includes an AJAX action (ajaxRefreshQrCode) that regenerates and returns new QR code data on demand from the frontend JavaScript. This action performs security checks on the order and customer.
Validation Controller (controllers/front/validation.php):
Processes the submitted UTR from the customer.
Adds the UTR as a private message to the corresponding PrestaShop order.
Redirects the customer to the order confirmation page.
IV. Admin & Reporting Features:
UTR Display in Order: The displayAdminOrderLeft hook displays the customer-submitted UTR directly in the Back Office order view for easy merchant reference.
Manual Refund Logging:
Provides a section within the admin order view (for orders placed with this module) where merchants can manually log details of UPI refunds they've processed (e.g., refund UTR, amount, date).
This helps in keeping refund records associated with the order within PrestaShop, even though the refund itself is processed externally.
Optionally allows changing the order status to "Refunded."
Reporting Dashboard:
A dedicated page/tab within the module's configuration or a separate Back Office section.
Lists UPI transactions processed through the module, showing details like Order ID, customer, amount, submitted UTR, current order status.
Provides merchants with a centralized overview of their UPI payment activity via this module.
Technical Stack & Dependencies:
PrestaShop Version: Designed for PrestaShop 8.1.x and above (can be adapted for older versions with UI/controller adjustments).
PHP: Standard PHP as required by PrestaShop.
Smarty: For PrestaShop templating.
JavaScript: For dynamic QR code refresh (using fetch API and setInterval), countdown timer, and potentially other client-side enhancements.
CSS: For styling the payment page/popup.
endroid/qr-code Library: (Composer dependency or manually included) for generating QR codes.
File System: Requires write permissions for the module's image upload directory.
Benefits:
For Merchants:
Cost-effective way to accept UPI payments.
Increased branding on the payment page.
Improved administrative workflow with UTR display, refund logging, and reporting.
Control over QR code behavior (static or dynamic).
For Customers:
Familiar and convenient UPI payment method.
Clear instructions and multiple ways to pay (QR, deep links).
Trust through visible shop branding.