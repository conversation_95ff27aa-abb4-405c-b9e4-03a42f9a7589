# FajaPay - PrestaShop UPI Payment Module

A PrestaShop payment module that enables Indian merchants to accept UPI payments with manual verification workflow.

## Features

- UPI QR Code payment integration
- Dynamic QR code refresh capability
- Direct deep links to popular UPI apps (Google Pay, PhonePe, Paytm)
- Manual payment verification with UTR tracking
- Custom order status for UPI payments
- Responsive design
- Admin dashboard integration

## Requirements

- PrestaShop 8.1.x or later
- PHP 7.4 or later with curl extension enabled

## Installation

1. Download and extract the module to your PrestaShop's `modules` directory
2. Install the module through PrestaShop's back office:
   - Go to Modules > Module Manager
   - Find "FajaPay" in the list
   - Click "Install"

## Configuration

1. Go to Modules > Module Manager > FajaPay > Configure
2. Enter your UPI ID
3. Set your shop name (as it will appear in UPI apps)
4. Configure QR code refresh settings:
   - Enable/disable automatic refresh
   - Set refresh interval (in seconds)
5. Save the configuration

## Usage

### For Customers

1. Select "Pay with UPI" during checkout
2. Scan the QR code with any UPI app
3. Make the payment
4. Enter the UTR (UPI Transaction Reference) number
5. Submit to complete the order

### For Merchants

1. View new UPI orders in the "Awaiting UPI Confirmation" status
2. Check the provided UTR number in order details
3. Verify the payment in your UPI app/bank account
4. Update the order status accordingly

## Security

- All user inputs are validated and sanitized
- CSRF protection implemented
- Secure payment workflow
- PrestaShop security best practices followed

## Support

For support, please contact:
- Email: <EMAIL>
- Developer: Dr.wolfsha

## License

Proprietary License - © 2025 FajaPay
Developed by Fowzana Soft Solutions (ZuhayFaja Group)

## Version History

- 0.0.1: Initial release
  - Basic UPI payment functionality
  - QR code generation
  - UTR tracking
  - Admin interface
