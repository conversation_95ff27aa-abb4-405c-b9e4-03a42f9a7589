<?php
/**
 * PHP QR Code Generator
 *
 * Lightweight, standards-compliant QR Code generator optimized for UPI payments
 * Based on ISO/IEC 18004:2015 specification
 * Includes Reed-Solomon error correction
 *
 * <AUTHOR> Module
 * @version 1.0.0
 * @license LGPL
 */

class PhpQrCode {
    // QR Code constants
    const MODE_NUMERIC = 1;
    const MODE_ALPHANUMERIC = 2;
    const MODE_BYTE = 4;
    const MODE_KANJI = 8;

    const ECC_LEVEL_L = 1; // ~7% correction
    const ECC_LEVEL_M = 0; // ~15% correction
    const ECC_LEVEL_Q = 3; // ~25% correction
    const ECC_LEVEL_H = 2; // ~30% correction

    // Version capacity table [version][ecc_level] = [numeric, alphanumeric, byte, kanji]
    private static $capacity = [
        1 => [
            self::ECC_LEVEL_L => [41, 25, 17, 10],
            self::ECC_LEVEL_M => [34, 20, 14, 8],
            self::ECC_LEVEL_Q => [27, 16, 11, 7],
            self::ECC_LEVEL_H => [17, 10, 7, 4]
        ],
        2 => [
            self::ECC_LEVEL_L => [77, 47, 32, 20],
            self::ECC_LEVEL_M => [63, 38, 26, 16],
            self::ECC_LEVEL_Q => [48, 29, 20, 12],
            self::ECC_LEVEL_H => [34, 20, 14, 8]
        ],
        3 => [
            self::ECC_LEVEL_L => [127, 77, 53, 32],
            self::ECC_LEVEL_M => [101, 61, 42, 26],
            self::ECC_LEVEL_Q => [77, 47, 32, 20],
            self::ECC_LEVEL_H => [58, 35, 24, 15]
        ],
        4 => [
            self::ECC_LEVEL_L => [187, 114, 78, 48],
            self::ECC_LEVEL_M => [149, 90, 62, 38],
            self::ECC_LEVEL_Q => [111, 67, 46, 28],
            self::ECC_LEVEL_H => [82, 50, 34, 21]
        ],
        5 => [
            self::ECC_LEVEL_L => [255, 154, 106, 65],
            self::ECC_LEVEL_M => [202, 122, 84, 52],
            self::ECC_LEVEL_Q => [144, 87, 60, 37],
            self::ECC_LEVEL_H => [106, 64, 44, 27]
        ]
    ];

    // Error correction codewords table [version][ecc_level] = [total_codewords, ecc_codewords, blocks]
    private static $eccTable = [
        1 => [
            self::ECC_LEVEL_L => [26, 7, 1],
            self::ECC_LEVEL_M => [26, 10, 1],
            self::ECC_LEVEL_Q => [26, 13, 1],
            self::ECC_LEVEL_H => [26, 17, 1]
        ],
        2 => [
            self::ECC_LEVEL_L => [44, 10, 1],
            self::ECC_LEVEL_M => [44, 16, 1],
            self::ECC_LEVEL_Q => [44, 22, 1],
            self::ECC_LEVEL_H => [44, 28, 1]
        ],
        3 => [
            self::ECC_LEVEL_L => [70, 15, 1],
            self::ECC_LEVEL_M => [70, 26, 1],
            self::ECC_LEVEL_Q => [70, 36, 2],
            self::ECC_LEVEL_H => [70, 44, 2]
        ],
        4 => [
            self::ECC_LEVEL_L => [100, 20, 1],
            self::ECC_LEVEL_M => [100, 36, 2],
            self::ECC_LEVEL_Q => [100, 52, 2],
            self::ECC_LEVEL_H => [100, 64, 4]
        ],
        5 => [
            self::ECC_LEVEL_L => [134, 26, 1],
            self::ECC_LEVEL_M => [134, 48, 2],
            self::ECC_LEVEL_Q => [134, 72, 4],
            self::ECC_LEVEL_H => [134, 88, 4]
        ]
    ];

    private $data;
    private $size;
    private $margin;
    private $eccLevel;
    private $version;
    private $moduleSize;

    public function __construct($data, $size = 300, $margin = 10, $eccLevel = self::ECC_LEVEL_H) {
        $this->data = $data;
        $this->size = $size;
        $this->margin = $margin;
        $this->eccLevel = $eccLevel;
        $this->version = $this->determineVersion($data, $eccLevel);
        $this->moduleSize = 21 + ($this->version - 1) * 4; // QR code module count
    }

    public function getDataUri() {
        try {
            // Generate QR code matrix
            $matrix = $this->generateMatrix();
            if (!$matrix) {
                return false;
            }

            // Check if GD is available
            if (extension_loaded('gd')) {
                // Create PNG image using GD
                $image = $this->createImage($matrix);
                if (!$image) {
                    return false;
                }

                // Convert to base64 data URI
                ob_start();
                imagepng($image);
                $imageData = ob_get_clean();
                imagedestroy($image);

                return 'data:image/png;base64,' . base64_encode($imageData);
            } else {
                // Fallback to SVG when GD is not available
                return $this->createSvgDataUri($matrix);
            }
        } catch (Exception $e) {
            return false;
        }
    }

    private function determineVersion($data, $eccLevel) {
        $dataLength = strlen($data);

        // Determine encoding mode (simplified for UPI strings)
        $mode = self::MODE_BYTE; // UPI strings are typically byte mode
        $modeIndex = 2; // byte mode index in capacity table

        // Find minimum version that can hold the data
        for ($version = 1; $version <= 5; $version++) {
            if (isset(self::$capacity[$version][$eccLevel][$modeIndex])) {
                if ($dataLength <= self::$capacity[$version][$eccLevel][$modeIndex]) {
                    return $version;
                }
            }
        }

        // Default to version 3 if data is too large
        return 3;
    }

    private function generateMatrix() {
        $size = $this->moduleSize;
        $matrix = array_fill(0, $size, array_fill(0, $size, 0));

        // Add function patterns
        $this->addFinderPatterns($matrix);
        $this->addSeparators($matrix);
        $this->addTimingPatterns($matrix);
        $this->addDarkModule($matrix);

        // Add alignment patterns for versions > 1
        if ($this->version > 1) {
            $this->addAlignmentPatterns($matrix);
        }

        // Encode data
        $dataCodewords = $this->encodeData();
        $eccCodewords = $this->generateECC($dataCodewords);
        $allCodewords = array_merge($dataCodewords, $eccCodewords);

        // Place data in matrix
        $this->placeData($matrix, $allCodewords);

        // Apply mask pattern
        $this->applyMask($matrix, 0); // Use mask pattern 0 for simplicity

        return $matrix;
    }

    private function addFinderPatterns(&$matrix) {
        $positions = [
            [0, 0], // Top-left
            [$this->moduleSize - 7, 0], // Top-right
            [0, $this->moduleSize - 7] // Bottom-left
        ];

        foreach ($positions as $pos) {
            $this->addFinderPattern($matrix, $pos[0], $pos[1]);
        }
    }

    private function addFinderPattern(&$matrix, $x, $y) {
        // 7x7 finder pattern
        for ($dy = 0; $dy < 7; $dy++) {
            for ($dx = 0; $dx < 7; $dx++) {
                $module = 0;

                // Outer border (7x7)
                if ($dx == 0 || $dx == 6 || $dy == 0 || $dy == 6) {
                    $module = 1;
                }
                // Inner square (3x3 centered)
                elseif ($dx >= 2 && $dx <= 4 && $dy >= 2 && $dy <= 4) {
                    $module = 1;
                }

                if ($x + $dx < $this->moduleSize && $y + $dy < $this->moduleSize) {
                    $matrix[$y + $dy][$x + $dx] = $module;
                }
            }
        }
    }

    private function addSeparators(&$matrix) {
        // Add white separators around finder patterns
        $positions = [
            [0, 0], // Top-left
            [$this->moduleSize - 8, 0], // Top-right
            [0, $this->moduleSize - 8] // Bottom-left
        ];

        foreach ($positions as $pos) {
            // Add 8x8 white border around 7x7 finder pattern
            for ($dy = -1; $dy <= 7; $dy++) {
                for ($dx = -1; $dx <= 7; $dx++) {
                    $x = $pos[0] + $dx;
                    $y = $pos[1] + $dy;

                    if ($x >= 0 && $x < $this->moduleSize && $y >= 0 && $y < $this->moduleSize) {
                        // Only set separator if not part of finder pattern
                        if ($dx == -1 || $dx == 7 || $dy == -1 || $dy == 7) {
                            $matrix[$y][$x] = 0;
                        }
                    }
                }
            }
        }
    }

    private function addTimingPatterns(&$matrix) {
        // Horizontal timing pattern
        for ($x = 8; $x < $this->moduleSize - 8; $x++) {
            $matrix[6][$x] = ($x % 2 == 0) ? 1 : 0;
        }

        // Vertical timing pattern
        for ($y = 8; $y < $this->moduleSize - 8; $y++) {
            $matrix[$y][6] = ($y % 2 == 0) ? 1 : 0;
        }
    }

    private function addDarkModule(&$matrix) {
        // Dark module at (4*version + 9, 8)
        $x = 8;
        $y = (4 * $this->version) + 9;
        if ($y < $this->moduleSize) {
            $matrix[$y][$x] = 1;
        }
    }

    private function addAlignmentPatterns(&$matrix) {
        // Alignment pattern positions for different versions
        $positions = [
            2 => [6, 18],
            3 => [6, 22],
            4 => [6, 26],
            5 => [6, 30]
        ];

        if (!isset($positions[$this->version])) {
            return;
        }

        $coords = $positions[$this->version];

        // Add alignment patterns at specified coordinates
        foreach ($coords as $x) {
            foreach ($coords as $y) {
                // Skip if overlaps with finder patterns
                if (($x <= 8 && $y <= 8) ||
                    ($x <= 8 && $y >= $this->moduleSize - 8) ||
                    ($x >= $this->moduleSize - 8 && $y <= 8)) {
                    continue;
                }

                $this->addAlignmentPattern($matrix, $x, $y);
            }
        }
    }

    private function addAlignmentPattern(&$matrix, $centerX, $centerY) {
        // 5x5 alignment pattern
        for ($dy = -2; $dy <= 2; $dy++) {
            for ($dx = -2; $dx <= 2; $dx++) {
                $x = $centerX + $dx;
                $y = $centerY + $dy;

                if ($x >= 0 && $x < $this->moduleSize && $y >= 0 && $y < $this->moduleSize) {
                    // Pattern: outer ring and center dot
                    if (abs($dx) == 2 || abs($dy) == 2 || ($dx == 0 && $dy == 0)) {
                        $matrix[$y][$x] = 1;
                    } else {
                        $matrix[$y][$x] = 0;
                    }
                }
            }
        }
    }

    private function encodeData() {
        $data = $this->data;
        $mode = self::MODE_BYTE;

        // Create bit stream
        $bitStream = [];

        // Mode indicator (4 bits)
        $this->appendBits($bitStream, $mode, 4);

        // Character count indicator
        $countBits = $this->getCharacterCountBits($mode, $this->version);
        $this->appendBits($bitStream, strlen($data), $countBits);

        // Data
        for ($i = 0; $i < strlen($data); $i++) {
            $this->appendBits($bitStream, ord($data[$i]), 8);
        }

        // Terminator (up to 4 bits)
        $terminatorBits = min(4, $this->getDataCapacityBits() - count($bitStream));
        for ($i = 0; $i < $terminatorBits; $i++) {
            $bitStream[] = 0;
        }

        // Pad to byte boundary
        while (count($bitStream) % 8 != 0) {
            $bitStream[] = 0;
        }

        // Convert to bytes
        $dataBytes = [];
        for ($i = 0; $i < count($bitStream); $i += 8) {
            $byte = 0;
            for ($j = 0; $j < 8; $j++) {
                if (isset($bitStream[$i + $j])) {
                    $byte = ($byte << 1) | $bitStream[$i + $j];
                }
            }
            $dataBytes[] = $byte;
        }

        // Pad with alternating bytes if needed
        $requiredBytes = self::$eccTable[$this->version][$this->eccLevel][0] - self::$eccTable[$this->version][$this->eccLevel][1];
        $padBytes = [236, 17]; // Standard pad bytes
        $padIndex = 0;

        while (count($dataBytes) < $requiredBytes) {
            $dataBytes[] = $padBytes[$padIndex % 2];
            $padIndex++;
        }

        return array_slice($dataBytes, 0, $requiredBytes);
    }

    private function getCharacterCountBits($mode, $version) {
        // Character count indicator lengths
        $lengths = [
            self::MODE_NUMERIC => [10, 12, 14],
            self::MODE_ALPHANUMERIC => [9, 11, 13],
            self::MODE_BYTE => [8, 16, 16],
            self::MODE_KANJI => [8, 10, 12]
        ];

        if ($version <= 9) return $lengths[$mode][0];
        if ($version <= 26) return $lengths[$mode][1];
        return $lengths[$mode][2];
    }

    private function getDataCapacityBits() {
        $totalCodewords = self::$eccTable[$this->version][$this->eccLevel][0];
        $eccCodewords = self::$eccTable[$this->version][$this->eccLevel][1];
        return ($totalCodewords - $eccCodewords) * 8;
    }

    private function appendBits(&$bitStream, $value, $length) {
        for ($i = $length - 1; $i >= 0; $i--) {
            $bitStream[] = ($value >> $i) & 1;
        }
    }

    private function generateECC($dataCodewords) {
        $eccInfo = self::$eccTable[$this->version][$this->eccLevel];
        $eccLength = $eccInfo[1];

        // Reed-Solomon generator polynomial coefficients for different ECC lengths
        $generators = [
            7 => [0, 87, 229, 146, 149, 238, 102, 21],
            10 => [0, 251, 67, 46, 61, 118, 70, 64, 94, 32, 45],
            13 => [0, 74, 152, 176, 100, 86, 100, 106, 104, 130, 218, 206, 140, 78],
            15 => [0, 8, 183, 61, 91, 202, 37, 51, 58, 58, 237, 140, 124, 5, 99, 105],
            16 => [0, 120, 104, 107, 109, 102, 161, 76, 3, 91, 191, 147, 169, 182, 194, 225, 120],
            17 => [0, 43, 139, 206, 78, 43, 239, 123, 206, 214, 147, 24, 99, 150, 39, 243, 163, 136],
            18 => [0, 215, 234, 158, 94, 184, 97, 118, 170, 79, 187, 152, 148, 252, 179, 5, 98, 96, 153],
            20 => [0, 17, 60, 79, 50, 61, 163, 26, 187, 202, 180, 221, 225, 83, 239, 156, 164, 212, 212, 188, 190],
            22 => [0, 210, 171, 247, 242, 93, 230, 14, 109, 221, 53, 200, 74, 8, 172, 98, 80, 219, 134, 160, 105, 165, 231],
            24 => [0, 229, 121, 135, 48, 211, 117, 251, 126, 159, 180, 169, 152, 192, 226, 228, 218, 111, 0, 117, 232, 87, 96, 227, 21],
            26 => [0, 173, 125, 158, 2, 103, 182, 118, 17, 145, 201, 111, 28, 165, 53, 161, 21, 245, 142, 13, 102, 48, 227, 153, 145, 218, 70],
            28 => [0, 168, 223, 200, 104, 224, 234, 108, 180, 110, 190, 195, 147, 205, 27, 232, 201, 21, 43, 245, 87, 42, 195, 212, 119, 242, 37, 9, 123],
            30 => [0, 41, 173, 145, 152, 216, 31, 179, 182, 50, 48, 110, 86, 239, 96, 222, 125, 42, 173, 226, 193, 224, 130, 156, 37, 251, 216, 238, 40, 192, 180]
        ];

        if (!isset($generators[$eccLength])) {
            // Fallback to simple repetition if generator not available
            return array_fill(0, $eccLength, 0);
        }

        $generator = $generators[$eccLength];
        $eccCodewords = array_fill(0, $eccLength, 0);

        // Perform Reed-Solomon encoding
        foreach ($dataCodewords as $data) {
            $coeff = $eccCodewords[0] ^ $data;

            for ($i = 0; $i < $eccLength - 1; $i++) {
                $eccCodewords[$i] = $eccCodewords[$i + 1] ^ $this->gfMul($generator[$i + 1], $coeff);
            }
            $eccCodewords[$eccLength - 1] = $this->gfMul($generator[$eccLength], $coeff);
        }

        return $eccCodewords;
    }

    private function gfMul($a, $b) {
        // Galois Field multiplication
        if ($a == 0 || $b == 0) {
            return 0;
        }

        // GF(256) log and antilog tables (simplified)
        static $logTable = null;
        static $antilogTable = null;

        if ($logTable === null) {
            $logTable = array_fill(0, 256, 0);
            $antilogTable = array_fill(0, 256, 0);

            $x = 1;
            for ($i = 0; $i < 255; $i++) {
                $antilogTable[$i] = $x;
                $logTable[$x] = $i;
                $x = ($x << 1) ^ (($x & 0x80) ? 0x11d : 0);
                $x &= 0xff;
            }
        }

        return $antilogTable[($logTable[$a] + $logTable[$b]) % 255];
    }

    private function placeData(&$matrix, $codewords) {
        $size = $this->moduleSize;
        $bitIndex = 0;
        $totalBits = count($codewords) * 8;

        // Convert codewords to bit array
        $bits = [];
        foreach ($codewords as $codeword) {
            for ($i = 7; $i >= 0; $i--) {
                $bits[] = ($codeword >> $i) & 1;
            }
        }

        // Place data using zigzag pattern
        $up = true;
        for ($col = $size - 1; $col > 0; $col -= 2) {
            // Skip timing column
            if ($col == 6) {
                $col--;
            }

            for ($row = 0; $row < $size; $row++) {
                $actualRow = $up ? ($size - 1 - $row) : $row;

                for ($c = 0; $c < 2; $c++) {
                    $x = $col - $c;
                    $y = $actualRow;

                    if ($this->isDataModule($matrix, $x, $y)) {
                        if ($bitIndex < count($bits)) {
                            $matrix[$y][$x] = $bits[$bitIndex++];
                        } else {
                            $matrix[$y][$x] = 0;
                        }
                    }
                }
            }
            $up = !$up;
        }
    }

    private function isDataModule(&$matrix, $x, $y) {
        // Check if this position is available for data (not a function pattern)

        // Finder patterns and separators
        if (($x <= 8 && $y <= 8) ||
            ($x >= $this->moduleSize - 8 && $y <= 8) ||
            ($x <= 8 && $y >= $this->moduleSize - 8)) {
            return false;
        }

        // Timing patterns
        if ($x == 6 || $y == 6) {
            return false;
        }

        // Dark module
        if ($x == 8 && $y == (4 * $this->version) + 9) {
            return false;
        }

        // Alignment patterns (simplified check)
        if ($this->version > 1) {
            $positions = [
                2 => [6, 18],
                3 => [6, 22],
                4 => [6, 26],
                5 => [6, 30]
            ];

            if (isset($positions[$this->version])) {
                foreach ($positions[$this->version] as $centerX) {
                    foreach ($positions[$this->version] as $centerY) {
                        if (abs($x - $centerX) <= 2 && abs($y - $centerY) <= 2) {
                            return false;
                        }
                    }
                }
            }
        }

        return true;
    }

    private function applyMask(&$matrix, $maskPattern) {
        $size = $this->moduleSize;

        for ($y = 0; $y < $size; $y++) {
            for ($x = 0; $x < $size; $x++) {
                if ($this->isDataModule($matrix, $x, $y)) {
                    $mask = false;

                    // Apply mask pattern 0: (row + column) mod 2 == 0
                    switch ($maskPattern) {
                        case 0:
                            $mask = (($y + $x) % 2) == 0;
                            break;
                        case 1:
                            $mask = ($y % 2) == 0;
                            break;
                        case 2:
                            $mask = ($x % 3) == 0;
                            break;
                        case 3:
                            $mask = (($y + $x) % 3) == 0;
                            break;
                        default:
                            $mask = (($y + $x) % 2) == 0;
                    }

                    if ($mask) {
                        $matrix[$y][$x] = $matrix[$y][$x] ? 0 : 1;
                    }
                }
            }
        }

        // Add format information
        $this->addFormatInfo($matrix, $maskPattern);
    }

    private function addFormatInfo(&$matrix, $maskPattern) {
        // Format information: ECC level + mask pattern
        $formatInfo = ($this->eccLevel << 3) | $maskPattern;

        // BCH error correction for format info
        $bchCode = $this->calculateBCH($formatInfo, 0x537); // BCH(15,5)
        $formatBits = ($formatInfo << 10) | $bchCode;

        // XOR with mask pattern
        $formatBits ^= 0x5412;

        // Place format info around top-left finder pattern
        for ($i = 0; $i < 15; $i++) {
            $bit = ($formatBits >> $i) & 1;

            if ($i < 6) {
                $matrix[8][$i] = $bit;
            } elseif ($i < 8) {
                $matrix[8][$i + 1] = $bit;
            } elseif ($i < 9) {
                $matrix[7][8] = $bit;
            } else {
                $matrix[14 - $i][8] = $bit;
            }
        }

        // Place format info around top-right and bottom-left finder patterns
        for ($i = 0; $i < 15; $i++) {
            $bit = ($formatBits >> $i) & 1;

            if ($i < 8) {
                $matrix[$this->moduleSize - 1 - $i][8] = $bit;
            } else {
                $matrix[8][$this->moduleSize - 15 + $i] = $bit;
            }
        }
    }

    private function calculateBCH($data, $poly) {
        $bch = $data;
        for ($i = 4; $i >= 0; $i--) {
            if (($bch >> ($i + 10)) & 1) {
                $bch ^= $poly << $i;
            }
        }
        return $bch & 0x3ff;
    }

    private function createImage($matrix) {
        $moduleCount = count($matrix);
        $pixelPerModule = ($this->size - 2 * $this->margin) / $moduleCount;

        // Create image
        $image = imagecreatetruecolor($this->size, $this->size);
        if (!$image) {
            return false;
        }

        // Set colors
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);

        // Fill background
        imagefill($image, 0, 0, $white);

        // Draw QR code modules
        for ($row = 0; $row < $moduleCount; $row++) {
            for ($col = 0; $col < $moduleCount; $col++) {
                if ($matrix[$row][$col]) {
                    $x1 = $this->margin + $col * $pixelPerModule;
                    $y1 = $this->margin + $row * $pixelPerModule;
                    $x2 = $x1 + $pixelPerModule - 1;
                    $y2 = $y1 + $pixelPerModule - 1;

                    imagefilledrectangle($image, $x1, $y1, $x2, $y2, $black);
                }
            }
        }

        return $image;
    }

    private function createSvgDataUri($matrix) {
        $moduleCount = count($matrix);
        $pixelPerModule = ($this->size - 2 * $this->margin) / $moduleCount;

        // Create SVG content
        $svg = '<?xml version="1.0" encoding="UTF-8"?>';
        $svg .= '<svg xmlns="http://www.w3.org/2000/svg" ';
        $svg .= 'width="' . $this->size . '" height="' . $this->size . '" ';
        $svg .= 'viewBox="0 0 ' . $this->size . ' ' . $this->size . '">';

        // White background
        $svg .= '<rect width="' . $this->size . '" height="' . $this->size . '" fill="white"/>';

        // Draw QR code modules
        for ($row = 0; $row < $moduleCount; $row++) {
            for ($col = 0; $col < $moduleCount; $col++) {
                if ($matrix[$row][$col]) {
                    $x = $this->margin + $col * $pixelPerModule;
                    $y = $this->margin + $row * $pixelPerModule;

                    $svg .= '<rect x="' . $x . '" y="' . $y . '" ';
                    $svg .= 'width="' . $pixelPerModule . '" height="' . $pixelPerModule . '" ';
                    $svg .= 'fill="black"/>';
                }
            }
        }

        $svg .= '</svg>';

        // Return as data URI
        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }
}
