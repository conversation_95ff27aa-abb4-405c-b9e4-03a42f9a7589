<?php
/**
 * 2025 FajaPay
 *
 * NOTICE OF LICENSE
 *
 * Developed by Fowzana Soft Solutions (ZuhayFaja Group)
 *
 * <AUTHOR>
 * @copyright 2025 FajaPay
 * @license   Proprietary License - It is forbidden to resell or redistribute copies of the module or modified copies of the module.
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class FajaPay extends PaymentModule
{
    private $errors = [];
    private $output = '';

    public function __construct()
    {
        $this->name = 'fajapay';
        $this->tab = 'payments_gateways';
        $this->version = '0.0.1';
        $this->author = 'Dr.wolfsha';
        $this->need_instance = 1;
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('FajaPay - UPI Payment Solution');
        $this->description = $this->l('Accept UPI payments with manual verification workflow');
        $this->confirmUninstall = $this->l('Are you sure you want to uninstall this module?');
        $this->ps_versions_compliancy = ['min' => '8.1.0', 'max' => _PS_VERSION_];
    }

    public function install()
    {
        // Create custom order state
        if (!$this->createOrderState()) {
            return false;
        }

        // Create necessary directories
        if (!$this->createDirectories()) {
            return false;
        }

        return parent::install() &&
            $this->registerHook('paymentOptions') &&
            $this->registerHook('displayAdminOrderLeft') &&
            $this->registerHook('header') &&
            $this->installDb() &&
            Configuration::updateValue('FAJAPAY_UPI_ID', '') &&
            Configuration::updateValue('FAJAPAY_SHOP_NAME', '') &&
            Configuration::updateValue('FAJAPAY_ENABLE_QR_REFRESH', 0) &&
            Configuration::updateValue('FAJAPAY_QR_REFRESH_INTERVAL', 30);
    }

    public function uninstall()
    {
        return parent::uninstall() &&
            $this->uninstallDb() &&
            Configuration::deleteByName('FAJAPAY_UPI_ID') &&
            Configuration::deleteByName('FAJAPAY_SHOP_NAME') &&
            Configuration::deleteByName('FAJAPAY_ENABLE_QR_REFRESH') &&
            Configuration::deleteByName('FAJAPAY_QR_REFRESH_INTERVAL');
    }

    protected function createOrderState()
    {
        $orderState = new OrderState();
        $orderState->name = array_fill(1, 24, 'Awaiting UPI Confirmation');
        $orderState->color = '#4169E1';
        $orderState->logable = true;
        $orderState->paid = false;
        $orderState->module_name = $this->name;
        $orderState->template = 'payment';
        $orderState->send_email = false;
        $orderState->hidden = false;
        $orderState->delivery = false;
        $orderState->invoice = false;
        $orderState->pdf_invoice = false;
        $orderState->pdf_delivery = false;
        
        if ($orderState->add()) {
            // Save the order state ID for later use
            Configuration::updateValue('FAJAPAY_OS_WAITING', $orderState->id);
            return true;
        }
        
        return false;
    }

    protected function createDirectories()
    {
        $directories = [
            _PS_MODULE_DIR_.$this->name.'/views/img/',
            _PS_MODULE_DIR_.$this->name.'/views/css/',
            _PS_MODULE_DIR_.$this->name.'/views/js/',
            _PS_MODULE_DIR_.$this->name.'/views/templates/front/',
            _PS_MODULE_DIR_.$this->name.'/views/templates/admin/',
            _PS_MODULE_DIR_.$this->name.'/controllers/front/',
            _PS_MODULE_DIR_.$this->name.'/controllers/admin/',
        ];

        foreach ($directories as $dir) {
            if (!file_exists($dir) && !mkdir($dir, 0755, true)) {
                $this->errors[] = $this->l('Failed to create directory: ') . $dir;
                return false;
            }
        }

        return true;
    }

    protected function installDb()
    {
        $sql = [];
        
        // Create refunds logging table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'fajapay_refunds` (
            `id_refund` int(11) NOT NULL AUTO_INCREMENT,
            `id_order` int(11) NOT NULL,
            `refund_utr` varchar(255) NOT NULL,
            `amount` decimal(20,6) NOT NULL,
            `date_add` datetime NOT NULL,
            PRIMARY KEY (`id_refund`),
            KEY `id_order` (`id_order`)
        ) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4;';

        foreach ($sql as $query) {
            if (!Db::getInstance()->execute($query)) {
                return false;
            }
        }

        return true;
    }

    protected function uninstallDb()
    {
        $sql = [];
        
        // Drop refunds logging table
        $sql[] = 'DROP TABLE IF EXISTS `'._DB_PREFIX_.'fajapay_refunds`;';

        foreach ($sql as $query) {
            if (!Db::getInstance()->execute($query)) {
                return false;
            }
        }

        return true;
    }

    public function getContent()
    {
        if (Tools::isSubmit('submit'.$this->name)) {
            $this->postProcess();
        }

        return $this->output.$this->renderForm();
    }

    protected function renderForm()
    {
        $fields_form = [
            'form' => [
                'legend' => [
                    'title' => $this->l('FajaPay Settings'),
                    'icon' => 'icon-cogs',
                ],
                'input' => [
                    [
                        'type' => 'text',
                        'label' => $this->l('UPI ID'),
                        'name' => 'FAJAPAY_UPI_ID',
                        'required' => true,
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Shop Name'),
                        'name' => 'FAJAPAY_SHOP_NAME',
                        'desc' => $this->l('This name will be displayed in UPI apps'),
                        'required' => true,
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Enable QR Code Refresh'),
                        'name' => 'FAJAPAY_ENABLE_QR_REFRESH',
                        'is_bool' => true,
                        'values' => [
                            [
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Yes'),
                            ],
                            [
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('No'),
                            ],
                        ],
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('QR Refresh Interval (seconds)'),
                        'name' => 'FAJAPAY_QR_REFRESH_INTERVAL',
                        'class' => 'fixed-width-sm',
                        'desc' => $this->l('How often the QR code should refresh (in seconds)'),
                    ],
                ],
                'submit' => [
                    'title' => $this->l('Save'),
                ],
            ],
        ];

        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->table = $this->table;
        $lang = new Language((int)Configuration::get('PS_LANG_DEFAULT'));
        $helper->default_form_language = $lang->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG') ? Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG') : 0;
        $helper->identifier = $this->identifier;
        $helper->submit_action = 'submit'.$this->name;
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false).'&configure='.$this->name.'&tab_module='.$this->tab.'&module_name='.$this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');
        $helper->tpl_vars = [
            'fields_value' => $this->getConfigFieldsValues(),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        ];

        return $helper->generateForm([$fields_form]);
    }

    protected function getConfigFieldsValues()
    {
        return [
            'FAJAPAY_UPI_ID' => Configuration::get('FAJAPAY_UPI_ID'),
            'FAJAPAY_SHOP_NAME' => Configuration::get('FAJAPAY_SHOP_NAME'),
            'FAJAPAY_ENABLE_QR_REFRESH' => Configuration::get('FAJAPAY_ENABLE_QR_REFRESH'),
            'FAJAPAY_QR_REFRESH_INTERVAL' => Configuration::get('FAJAPAY_QR_REFRESH_INTERVAL'),
        ];
    }

    public function hookPaymentOptions($params)
    {
        if (!$this->active) {
            return [];
        }

        if (!$this->checkCurrency($params['cart'])) {
            return [];
        }

        $payment_options = [];

        $option = new PrestaShop\PrestaShop\Core\Payment\PaymentOption();
        $option->setCallToActionText($this->l('Pay with UPI'))
               ->setAction($this->context->link->getModuleLink($this->name, 'payment', [], true));

        $payment_options[] = $option;

        return $payment_options;
    }

    public function hookDisplayAdminOrderLeft($params)
    {
        $order = new Order($params['id_order']);
        if ($order->module !== $this->name) {
            return '';
        }

        $messages = Message::getMessagesByOrderId($order->id, true);
        $utr = '';
        foreach ($messages as $message) {
            if (strpos($message['message'], 'Customer provided UTR:') !== false) {
                $utr = trim(str_replace('Customer provided UTR:', '', $message['message']));
                break;
            }
        }

        $this->context->smarty->assign([
            'utr_number' => $utr,
            'order_reference' => $order->reference,
        ]);

        return $this->display(__FILE__, 'views/templates/admin/order_info.tpl');
    }

    public function hookHeader($params)
    {
        if (Tools::getValue('controller') === 'payment') {
            $this->context->controller->registerStylesheet(
                'fajapay-style',
                'modules/'.$this->name.'/views/css/front.css',
                [
                    'media' => 'all',
                    'priority' => 200,
                ]
            );
        }
    }

    public function checkCurrency($cart)
    {
        $currency_order = new Currency($cart->id_currency);
        $currencies_module = $this->getCurrency($cart->id_currency);

        if (is_array($currencies_module)) {
            foreach ($currencies_module as $currency_module) {
                if ($currency_order->id == $currency_module['id_currency']) {
                    return true;
                }
            }
        }
        return false;
    }

    protected function postProcess()
    {
        if (Tools::isSubmit('submit'.$this->name)) {
            $upi_id = Tools::getValue('FAJAPAY_UPI_ID');
            if (empty($upi_id)) {
                $this->output .= $this->displayError($this->l('UPI ID is required.'));
                return false;
            }

            $shop_name = Tools::getValue('FAJAPAY_SHOP_NAME');
            if (empty($shop_name)) {
                $this->output .= $this->displayError($this->l('Shop Name is required.'));
                return false;
            }

            Configuration::updateValue('FAJAPAY_UPI_ID', $upi_id);
            Configuration::updateValue('FAJAPAY_SHOP_NAME', $shop_name);
            Configuration::updateValue('FAJAPAY_ENABLE_QR_REFRESH', (int)Tools::getValue('FAJAPAY_ENABLE_QR_REFRESH'));
            Configuration::updateValue('FAJAPAY_QR_REFRESH_INTERVAL', (int)Tools::getValue('FAJAPAY_QR_REFRESH_INTERVAL'));

            $this->output .= $this->displayConfirmation($this->l('Settings updated successfully.'));
        }
    }
}
?>
