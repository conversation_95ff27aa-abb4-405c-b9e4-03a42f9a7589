{extends file='page.tpl'}

{block name='page_content'}
<div class="fajapay-payment-page card">
    <div class="card-header">
        <h3 class="card-header-title h3">{l s='UPI Payment' d='Modules.Fajapay.Shop'}</h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 text-center">
                <div class="qr-code-container mb-3">
                    <div id="qr-code-wrapper" class="mb-3">
                        <div id="qr-loading" class="text-center mb-2">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">{l s='Loading QR Code...' d='Modules.Fajapay.Shop'}</span>
                            </div>
                        </div>
                        <div id="qr-error" class="alert alert-danger" style="display: none;">
                            <i class="material-icons">error</i>
                            <span id="qr-error-message">{l s='Error loading QR code. Please refresh the page.' d='Modules.Fajapay.Shop'}</span>
                        </div>
                        <img id="qr-code" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" 
                             alt="UPI QR Code" class="img-fluid" style="display: none;"/>
                    </div>
                    {if $enableQrRefresh}
                        <div class="refresh-timer small text-muted mb-2">
                            {l s='QR Code refreshes in' d='Modules.Fajapay.Shop'} <span id="countdown">{$qrRefreshInterval}</span> {l s='seconds' d='Modules.Fajapay.Shop'}
                        </div>
                    {/if}
                </div>
                <div class="upi-apps-container mb-4">
                    <p class="mb-2">{l s='Open in UPI App:' d='Modules.Fajapay.Shop'}</p>
                    <div class="upi-deep-links">
                        <a href="#" class="btn btn-outline-primary btn-sm m-1 gpay-link" data-upi="">
                            <i class="material-icons">account_balance_wallet</i> Google Pay
                        </a>
                        <a href="#" class="btn btn-outline-primary btn-sm m-1 phonepe-link" data-upi="">
                            <i class="material-icons">phone_android</i> PhonePe
                        </a>
                        <a href="#" class="btn btn-outline-primary btn-sm m-1 paytm-link" data-upi="">
                            <i class="material-icons">payment</i> Paytm
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="order-summary mb-4">
                    <h4>{l s='Order Summary' d='Modules.Fajapay.Shop'}</h4>
                    <table class="table table-striped">
                        <tr>
                            <td>{l s='Total Items:' d='Modules.Fajapay.Shop'}</td>
                            <td>{$nbProducts}</td>
                        </tr>
                        <tr>
                            <td>{l s='Total Amount:' d='Modules.Fajapay.Shop'}</td>
                            <td class="price">{$total}</td>
                        </tr>
                    </table>
                </div>
                <div class="payment-instructions mb-4">
                    <h4>{l s='Payment Instructions' d='Modules.Fajapay.Shop'}</h4>
                    <ol class="pl-3">
                        <li>{l s='Scan the QR code using any UPI app' d='Modules.Fajapay.Shop'}</li>
                        <li>{l s='Complete the payment in your UPI app' d='Modules.Fajapay.Shop'}</li>
                        <li>{l s='Copy the UPI Transaction ID (UTR)' d='Modules.Fajapay.Shop'}</li>
                        <li>{l s='Submit the UTR below' d='Modules.Fajapay.Shop'}</li>
                    </ol>
                </div>
                <form action="{$link->getModuleLink('fajapay', 'payment', [], true)|escape:'html'}" method="post" class="utr-form">
                    <div class="form-group">
                        <label for="utr_number" class="form-label required">
                            {l s='UPI Transaction ID (UTR)' d='Modules.Fajapay.Shop'}
                        </label>
                        <input type="text" class="form-control" name="utr_number" id="utr_number" required 
                               placeholder="{l s='Enter your UPI Transaction ID' d='Modules.Fajapay.Shop'}"/>
                        <small class="form-text text-muted">
                            {l s='You can find the UTR in your UPI app\'s transaction history' d='Modules.Fajapay.Shop'}
                        </small>
                    </div>
                    <button type="submit" class="btn btn-primary btn-block mt-3">
                        {l s='Confirm Payment' d='Modules.Fajapay.Shop'}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

{literal}
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        let qrRefreshInterval = {/literal}{$qrRefreshInterval|intval}{literal};
        let countdown = qrRefreshInterval;
        let countdownElement = document.getElementById('countdown');
        let qrCodeElement = document.getElementById('qr-code');
        
        // Function to update UPI deep links
        function updateUpiLinks(upiString) {
            document.querySelector('.gpay-link').href = 'tez://upi/pay?pa=' + encodeURIComponent(upiString);
            document.querySelector('.phonepe-link').href = 'phonepe://pay?pa=' + encodeURIComponent(upiString);
            document.querySelector('.paytm-link').href = 'paytmmp://pay?pa=' + encodeURIComponent(upiString);
        }

        // Function to refresh QR code
        function refreshQrCode() {
            document.getElementById('qr-loading').style.display = 'block';
            document.getElementById('qr-code').style.display = 'none';
            document.getElementById('qr-error').style.display = 'none';

            let ajaxUrl = '{/literal}{$link->getModuleLink('fajapay', 'ajax', [], true)|escape:'javascript':'UTF-8'}{literal}';
            let token = '{/literal}{Tools::getToken(false)}{literal}';
            
            fetch(ajaxUrl + '?action=refreshQr&token=' + encodeURIComponent(token), {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
                cache: 'no-cache'
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.message || 'Server error: ' + response.status);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    qrCodeElement.src = data.data.qrCode;
                    qrCodeElement.style.display = 'block';
                    updateUpiLinks(data.data.upiString);
                    countdown = qrRefreshInterval;
                } else {
                    throw new Error(data.message || 'Failed to generate QR code');
                }
            })
            .catch(error => {
                console.error('Error refreshing QR code:', error);
                document.getElementById('qr-error').style.display = 'block';
                document.getElementById('qr-error-message').textContent = error.message || 'Error loading QR code. Please refresh the page.';
            })
            .finally(() => {
                document.getElementById('qr-loading').style.display = 'none';
            });
        }

        // Initial QR code generation
        refreshQrCode();

        {/literal}{if $enableQrRefresh}{literal}
        // Update countdown timer
        setInterval(() => {
            if (countdown > 0) {
                countdown--;
                if (countdownElement) {
                    countdownElement.textContent = countdown;
                }
            }
            if (countdown === 0) {
                refreshQrCode();
            }
        }, 1000);
        {/literal}{/if}{literal}
    });
</script>
{/literal}

{/block}
