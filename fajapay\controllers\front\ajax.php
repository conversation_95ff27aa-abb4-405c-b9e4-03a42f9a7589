<?php
/**
 * 2025 FajaPay
 *
 * NOTICE OF LICENSE
 *
 * Developed by Fowzana Soft Solutions (ZuhayFaja Group)
 *
 * <AUTHOR>
 * @copyright 2025 FajaPay
 * @license   Proprietary License
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_ . 'fajapay/lib/phpqrcode.php';

class FajaPayAjaxModuleFrontController extends ModuleFrontController
{
    private function sendJsonResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        die(json_encode($data));
    }

    public function init()
    {
        parent::init();
        $this->ajax = true;
        $this->content_only = true;

        // Initialize shop context
        if (Shop::isFeatureActive()) {
            Shop::setContext(Shop::CONTEXT_SHOP, (int)$this->context->shop->id);
        }
    }

    public function initContent()
    {
        try {
            // Verify AJAX request
            if (!$this->ajax || empty($_SERVER['HTTP_X_REQUESTED_WITH']) ||
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
                throw new Exception('Invalid request type', 400);
            }

            // Security token validation
            $token = Tools::getValue('token');
            if (empty($token) || $token !== Tools::getToken(false)) {
                throw new Exception('Invalid security token', 403);
            }

            // Process action
            $action = Tools::getValue('action');
            if (empty($action)) {
                throw new Exception('No action specified', 400);
            }

            switch ($action) {
                case 'refreshQr':
                    $response = $this->refreshQrCode();
                    break;
                case 'confirmPayment':
                    $response = $this->confirmPayment();
                    break;
                default:
                    throw new Exception('Invalid action: ' . $action, 400);
            }

            $this->sendJsonResponse($response);

        } catch (Exception $e) {
            $this->sendJsonResponse([
                'status' => 'error',
                'message' => $e->getMessage()
            ], $e->getCode() ?: 500);
        }
    }

    protected function refreshQrCode()
    {
        if (!$this->module->active) {
            throw new Exception('Module is not active', 400);
        }

        if (!$this->context->cart || !$this->context->cart->id) {
            throw new Exception('No active cart found', 400);
        }

        $cart = new Cart($this->context->cart->id);
        if (!Validate::isLoadedObject($cart)) {
            throw new Exception('Invalid cart', 400);
        }

        $upiId = Configuration::get('FAJAPAY_UPI_ID');
        $shopName = Configuration::get('FAJAPAY_SHOP_NAME');

        if (empty($upiId) || empty($shopName)) {
            throw new Exception('Module not properly configured', 500);
        }

        $total = $cart->getOrderTotal(true, Cart::BOTH);
        $reference = $cart->id . '-' . time();

        // Create UPI payment string
        $upiString = sprintf(
            'upi://pay?pa=%s&pn=%s&am=%.2f&tn=%s&tr=%s',
            urlencode($upiId),
            urlencode($shopName),
            $total,
            urlencode("Order Payment"),
            urlencode($reference)
        );

        try {
            // Generate QR code (supports both PNG with GD and SVG fallback)
            $qrCode = new PhpQrCode($upiString, 300, 10);
            $qrImage = $qrCode->getDataUri();

            if (!$qrImage) {
                throw new Exception('Failed to generate QR code image');
            }

            // Return the QR code data
            return [
                'status' => 'success',
                'data' => [
                    'qrCode' => $qrImage,
                    'reference' => $reference,
                    'upiString' => $upiString,
                    'format' => extension_loaded('gd') ? 'PNG' : 'SVG'
                ]
            ];
        } catch (Exception $e) {
            throw new Exception('QR code generation failed: ' . $e->getMessage(), 500);
        }
    }

    protected function confirmPayment()
    {
        if (!$this->module->active) {
            throw new Exception('Module is not active', 400);
        }

        // Get UTR number from request
        $utrNumber = Tools::getValue('utr_number');
        if (empty($utrNumber)) {
            throw new Exception('UTR number is required', 400);
        }

        // Validate UTR format (basic validation)
        if (!preg_match('/^[A-Z0-9]{12}$/', $utrNumber)) {
            throw new Exception('Invalid UTR format. UTR should be 12 characters alphanumeric.', 400);
        }

        // Get order ID
        $orderId = (int)Tools::getValue('order_id');
        if (empty($orderId)) {
            throw new Exception('Order ID is required', 400);
        }

        // Validate order
        $order = new Order($orderId);
        if (!Validate::isLoadedObject($order)) {
            throw new Exception('Invalid order', 400);
        }

        // Check if order belongs to current customer
        if ($order->id_customer != $this->context->customer->id) {
            throw new Exception('Access denied', 403);
        }

        // Check if order was paid with this module
        if ($order->module !== $this->module->name) {
            throw new Exception('This order was not paid with UPI', 400);
        }

        try {
            // Check if UTR already exists for this order
            $existingUtr = $this->getOrderUTR($orderId);
            if (!empty($existingUtr)) {
                throw new Exception('UTR already provided for this order', 400);
            }

            // Store UTR in database
            $this->storeUTR($orderId, $utrNumber, $order->total_paid);

            // Add message to order
            $message = new Message();
            $message->id_order = $orderId;
            $message->message = 'Customer provided UTR: ' . $utrNumber;
            $message->id_customer = $order->id_customer;
            $message->private = 0;
            $message->add();

            // Generate order details URL
            $orderDetailsUrl = $this->context->link->getPageLink(
                'order-detail',
                true,
                null,
                ['id_order' => $orderId]
            );

            return [
                'status' => 'success',
                'message' => 'Payment confirmation received successfully',
                'data' => [
                    'utr_number' => $utrNumber,
                    'order_id' => $orderId,
                    'redirect_url' => $orderDetailsUrl
                ]
            ];

        } catch (Exception $e) {
            throw new Exception('Failed to confirm payment: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Store UTR in database
     */
    private function storeUTR($orderId, $utrNumber, $amount)
    {
        $sql = 'INSERT INTO `'._DB_PREFIX_.'fajapay_transactions`
                (id_order, utr_number, transaction_amount, payment_status, date_add, date_upd)
                VALUES ('.(int)$orderId.', "'.pSQL($utrNumber).'", '.(float)$amount.', "pending", NOW(), NOW())';

        if (!Db::getInstance()->execute($sql)) {
            throw new Exception('Failed to store UTR in database');
        }
    }

    /**
     * Get UTR information for an order
     */
    private function getOrderUTR($orderId)
    {
        $sql = 'SELECT utr_number, transaction_amount, payment_status, date_add
                FROM `'._DB_PREFIX_.'fajapay_transactions`
                WHERE id_order = '.(int)$orderId.'
                ORDER BY date_add DESC
                LIMIT 1';

        $result = Db::getInstance()->getRow($sql);
        return $result ? $result : [];
    }
}
