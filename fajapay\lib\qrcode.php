<?php
/**
 * PHP QR Code
 * 
 * Lightweight QR Code generator
 * For FajaPay module
 */

class SimpleQrCode {
    private $data;
    private $size;
    private $margin;
    
    public function __construct($data, $size = 300, $margin = 10) {
        $this->data = $data;
        $this->size = $size;
        $this->margin = $margin;
    }
    
    public function getBinaryQrcode() {
        // Create a QR Code using Google Charts API
        $ch = curl_init();
        $url = 'https://chart.googleapis.com/chart?cht=qr&chs=' . $this->size . 'x' . $this->size . 
               '&chld=H|' . $this->margin . '&chl=' . urlencode($this->data);
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        return $response;
    }
    
    public function getDataUri() {
        $binary = $this->getBinaryQrcode();
        return 'data:image/png;base64,' . base64_encode($binary);
    }
}
